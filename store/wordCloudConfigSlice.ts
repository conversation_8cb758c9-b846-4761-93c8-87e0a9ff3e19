import { createSlice, PayloadAction } from "@reduxjs/toolkit"

// 螺旋类型
export const SPIRAL_TYPES = {
  archimedean: "archimedean",
  rectangular: "rectangular"
} as const

export type SpiralType = keyof typeof SPIRAL_TYPES

// 词云配置接口
export interface WordCloudConfig {
  // 字体大小范围
  minFontSize: number
  maxFontSize: number

  // 旋转角度设置
  rotationEnabled: boolean
  randomRotation: boolean // 是否随机旋转
  rotationAngle: number // 固定旋转角度（当randomRotation为false时使用）

  // 布局设置
  spiralType: SpiralType
  padding: number

  // 尺寸设置
  width: number
  height: number
}

// 默认配置
export const defaultWordCloudConfig: WordCloudConfig = {
  // 字体大小范围
  minFontSize: 12,
  maxFontSize: 48,

  // 旋转角度设置
  rotationEnabled: true,
  randomRotation: true,
  rotationAngle: 45, // 默认45度旋转

  // 布局设置
  spiralType: "archimedean",
  padding: 5,

  // 尺寸设置
  width: 400,
  height: 300
}

// 创建slice
export const wordCloudConfigSlice = createSlice({
  name: "wordCloudConfig",
  initialState: defaultWordCloudConfig,
  reducers: {
    // 更新字体大小范围
    updateFontSizeRange: (state, action: PayloadAction<{
      minFontSize?: number
      maxFontSize?: number
    }>) => {
      if (action.payload.minFontSize !== undefined) {
        state.minFontSize = Math.max(1, action.payload.minFontSize)
      }
      if (action.payload.maxFontSize !== undefined) {
        state.maxFontSize = Math.max(state.minFontSize + 1, action.payload.maxFontSize)
      }
    },

    // 更新旋转设置
    updateRotationSettings: (state, action: PayloadAction<{
      rotationEnabled?: boolean
      randomRotation?: boolean
      rotationAngle?: number
    }>) => {
      if (action.payload.rotationAngle !== undefined) {
        // 确保旋转角度在合理范围内
        state.rotationAngle = Math.max(-90, Math.min(90, action.payload.rotationAngle))
      }
      if (action.payload.rotationEnabled !== undefined) {
        state.rotationEnabled = action.payload.rotationEnabled
      }
      if (action.payload.randomRotation !== undefined) {
        state.randomRotation = action.payload.randomRotation
      }
    },

    // 更新布局设置
    updateLayoutSettings: (state, action: PayloadAction<{
      spiralType?: SpiralType
      padding?: number
    }>) => {
      Object.assign(state, action.payload)
    },

    // 更新尺寸设置
    updateDimensions: (state, action: PayloadAction<{
      width?: number
      height?: number
    }>) => {
      if (action.payload.width !== undefined) {
        state.width = Math.max(100, action.payload.width)
      }
      if (action.payload.height !== undefined) {
        state.height = Math.max(100, action.payload.height)
      }
    },

    // 重置到默认配置
    resetToDefaults: () => defaultWordCloudConfig,

    // 更新完整配置
    updateConfig: (state, action: PayloadAction<Partial<WordCloudConfig>>) => {
      Object.assign(state, action.payload)
    }
  }
})

// 导出actions
export const {
  updateFontSizeRange,
  updateRotationSettings,
  updateLayoutSettings,
  updateDimensions,
  resetToDefaults,
  updateConfig
} = wordCloudConfigSlice.actions

// 导出selectors
export const selectWordCloudConfig = (state: { wordCloudConfig: WordCloudConfig }) => state.wordCloudConfig

export default wordCloudConfigSlice.reducer

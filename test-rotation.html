<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>字体旋转功能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .post-list {
            list-style: none;
            padding: 0;
        }
        .post-item {
            margin: 10px 0;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .post-title {
            font-weight: bold;
            color: #333;
        }
        .post-meta {
            color: #666;
            font-size: 0.9em;
        }
        .instructions {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>字体旋转功能测试页面</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ol>
            <li>打开浏览器扩展的词云面板</li>
            <li>在设置页面中测试不同的旋转配置：
                <ul>
                    <li>启用/禁用文字旋转</li>
                    <li>启用/禁用随机旋转</li>
                    <li>调整固定旋转角度（当随机旋转关闭时）</li>
                </ul>
            </li>
            <li>观察词云中文字的旋转效果</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>技术文章列表</h2>
        <ul class="post-list">
            <li class="post-item">
                <div class="post-title">React Hooks 深入理解与实践</div>
                <div class="post-meta">作者：张三 | 时间：2024-01-15</div>
            </li>
            <li class="post-item">
                <div class="post-title">TypeScript 高级类型系统详解</div>
                <div class="post-meta">作者：李四 | 时间：2024-01-14</div>
            </li>
            <li class="post-item">
                <div class="post-title">Vue 3 Composition API 最佳实践</div>
                <div class="post-meta">作者：王五 | 时间：2024-01-13</div>
            </li>
            <li class="post-item">
                <div class="post-title">JavaScript 性能优化技巧</div>
                <div class="post-meta">作者：赵六 | 时间：2024-01-12</div>
            </li>
            <li class="post-item">
                <div class="post-title">CSS Grid 布局完全指南</div>
                <div class="post-meta">作者：孙七 | 时间：2024-01-11</div>
            </li>
            <li class="post-item">
                <div class="post-title">Node.js 微服务架构设计</div>
                <div class="post-meta">作者：周八 | 时间：2024-01-10</div>
            </li>
            <li class="post-item">
                <div class="post-title">前端工程化工具链搭建</div>
                <div class="post-meta">作者：吴九 | 时间：2024-01-09</div>
            </li>
            <li class="post-item">
                <div class="post-title">React Native 跨平台开发实战</div>
                <div class="post-meta">作者：郑十 | 时间：2024-01-08</div>
            </li>
            <li class="post-item">
                <div class="post-title">GraphQL API 设计与实现</div>
                <div class="post-meta">作者：钱一 | 时间：2024-01-07</div>
            </li>
            <li class="post-item">
                <div class="post-title">Docker 容器化部署指南</div>
                <div class="post-meta">作者：孙二 | 时间：2024-01-06</div>
            </li>
        </ul>
    </div>

    <div class="test-section">
        <h2>前端框架对比</h2>
        <ul class="post-list">
            <li class="post-item">
                <div class="post-title">React vs Vue vs Angular 性能对比</div>
                <div class="post-meta">作者：李三 | 时间：2024-01-05</div>
            </li>
            <li class="post-item">
                <div class="post-title">Svelte 新兴框架深度解析</div>
                <div class="post-meta">作者：王四 | 时间：2024-01-04</div>
            </li>
            <li class="post-item">
                <div class="post-title">Next.js 全栈开发实践</div>
                <div class="post-meta">作者：张五 | 时间：2024-01-03</div>
            </li>
            <li class="post-item">
                <div class="post-title">Nuxt.js 服务端渲染优化</div>
                <div class="post-meta">作者：赵六 | 时间：2024-01-02</div>
            </li>
            <li class="post-item">
                <div class="post-title">Gatsby 静态站点生成器使用指南</div>
                <div class="post-meta">作者：孙七 | 时间：2024-01-01</div>
            </li>
        </ul>
    </div>

    <script>
        // 添加一些动态内容以便测试
        console.log('测试页面已加载，可以开始测试字体旋转功能');
        
        // 模拟一些动态加载的内容
        setTimeout(() => {
            const newPost = document.createElement('li');
            newPost.className = 'post-item';
            newPost.innerHTML = `
                <div class="post-title">WebAssembly 在前端的应用前景</div>
                <div class="post-meta">作者：动态加载 | 时间：${new Date().toLocaleDateString()}</div>
            `;
            document.querySelector('.post-list').appendChild(newPost);
        }, 2000);
    </script>
</body>
</html>

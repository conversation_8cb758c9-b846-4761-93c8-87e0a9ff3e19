import React, { useState } from "react"
import { useAppDispatch, useAppSelector } from "../store/store"
import {
  updateFontSizeRange,
  updateRotationSettings,
  updateLayoutSettings,
  updateDimensions,
  resetToDefaults,
  selectWordCloudConfig,
  SPIRAL_TYPES,
  type SpiralType
} from "../store/wordCloudConfigSlice"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card"
import { Label } from "./ui/label"
import { Slider } from "./ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { Checkbox } from "./ui/checkbox"
import { Button } from "./ui/button"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./ui/alert-dialog"
import { RotateCcw, Type, Layout, Eye } from "lucide-react"
import WordCloudComponent from "./wordcloud/WordCloud"

// 预览用的示例数据
const PREVIEW_WORDS = [
  { text: "React", size: 100, relatedPosts: [] },
  { text: "TypeScript", size: 80, relatedPosts: [] },
  { text: "JavaScript", size: 90, relatedPosts: [] },
  { text: "词云", size: 70, relatedPosts: [] },
  { text: "配置", size: 60, relatedPosts: [] },
  { text: "样式", size: 50, relatedPosts: [] },
  { text: "颜色", size: 45, relatedPosts: [] },
  { text: "字体", size: 40, relatedPosts: [] },
  { text: "布局", size: 35, relatedPosts: [] },
  { text: "动画", size: 30, relatedPosts: [] },
  { text: "预览", size: 25, relatedPosts: [] },
  { text: "设置", size: 20, relatedPosts: [] }
]

export function WordCloudSettings() {
  const dispatch = useAppDispatch()
  const config = useAppSelector(selectWordCloudConfig)
  const [showPreview, setShowPreview] = useState<boolean>(true)

  const handleResetToDefaults = () => {
    dispatch(resetToDefaults())
  }

  return (
    <div className="space-y-6">
      {/* 页面标题和控制按钮 */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">词云样式配置</h2>
          <p className="text-muted-foreground">自定义词云的显示样式和行为</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowPreview(!showPreview)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {showPreview ? "隐藏预览" : "显示预览"}
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="outline"
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                重置默认
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>确认重置配置</AlertDialogTitle>
                <AlertDialogDescription>
                  这将重置所有词云配置到默认值。此操作无法撤销，您确定要继续吗？
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>取消</AlertDialogCancel>
                <AlertDialogAction onClick={handleResetToDefaults}>
                  确认重置
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* 预览区域 */}
      {showPreview && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              实时预览
            </CardTitle>
            <CardDescription>
              预览当前配置的词云效果，配置更改会立即反映在预览中
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex justify-center">
              <WordCloudComponent words={PREVIEW_WORDS} />
            </div>
          </CardContent>
        </Card>
      )}

      {/* 配置区域 - 使用网格布局 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 字体设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Type className="h-5 w-5" />
              字体设置
            </CardTitle>
            <CardDescription>配置词云中文字的字体大小和旋转</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 字体大小范围 */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>最小字体大小: {config.minFontSize}px</Label>
                <Slider
                  value={[config.minFontSize]}
                  onValueChange={([value]) =>
                    dispatch(updateFontSizeRange({ minFontSize: value }))
                  }
                  min={8}
                  max={32}
                  step={1}
                />
              </div>
              <div className="space-y-2">
                <Label>最大字体大小: {config.maxFontSize}px</Label>
                <Slider
                  value={[config.maxFontSize]}
                  onValueChange={([value]) =>
                    dispatch(updateFontSizeRange({ maxFontSize: value }))
                  }
                  min={config.minFontSize + 1}
                  max={100}
                  step={1}
                />
              </div>
            </div>

            {/* 旋转设置 */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="rotation-enabled"
                  checked={config.rotationEnabled}
                  onCheckedChange={(checked) =>
                    dispatch(updateRotationSettings({ rotationEnabled: !!checked }))
                  }
                />
                <Label htmlFor="rotation-enabled">启用文字旋转</Label>
              </div>

              {config.rotationEnabled && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="random-rotation"
                      checked={config.randomRotation}
                      onCheckedChange={(checked) =>
                        dispatch(updateRotationSettings({ randomRotation: !!checked }))
                      }
                    />
                    <Label htmlFor="random-rotation">随机旋转角度</Label>
                  </div>

                  {!config.randomRotation && (
                    <div className="space-y-2">
                      <Label>固定旋转角度: {config.rotationAngle}°</Label>
                      <Slider
                        value={[config.rotationAngle]}
                        onValueChange={([value]) =>
                          dispatch(updateRotationSettings({ rotationAngle: value }))
                        }
                        min={-90}
                        max={90}
                        step={15}
                      />
                      <div className="text-xs text-muted-foreground">
                        -90°（逆时针垂直）到 90°（顺时针垂直）
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 布局设置 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layout className="h-5 w-5" />
              布局设置
            </CardTitle>
            <CardDescription>配置词云的布局算法和间距</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 螺旋类型 */}
            <div className="space-y-2">
              <Label>布局算法</Label>
              <Select
                value={config.spiralType}
                onValueChange={(value: SpiralType) =>
                  dispatch(updateLayoutSettings({ spiralType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="archimedean">阿基米德螺旋</SelectItem>
                  <SelectItem value="rectangular">矩形螺旋</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 词语间距 */}
            <div className="space-y-2">
              <Label>词语间距: {config.padding}px</Label>
              <Slider
                value={[config.padding]}
                onValueChange={([value]) =>
                  dispatch(updateLayoutSettings({ padding: value }))
                }
                min={0}
                max={20}
                step={1}
              />
            </div>

            {/* 尺寸设置 */}
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>宽度: {config.width}px</Label>
                <Slider
                  value={[config.width]}
                  onValueChange={([value]) =>
                    dispatch(updateDimensions({ width: value }))
                  }
                  min={200}
                  max={1200}
                  step={50}
                />
              </div>
              <div className="space-y-2">
                <Label>高度: {config.height}px</Label>
                <Slider
                  value={[config.height]}
                  onValueChange={([value]) =>
                    dispatch(updateDimensions({ height: value }))
                  }
                  min={150}
                  max={800}
                  step={50}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
